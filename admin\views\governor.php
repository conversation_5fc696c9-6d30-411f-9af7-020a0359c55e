<?php
/**
 * Provincial Administration Manager - Governor Profile View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get governor data
$governors = get_posts(array(
    'post_type' => 'esp_governor',
    'numberposts' => 1,
    'post_status' => 'any'
));

$governor = !empty($governors) ? $governors[0] : null;
$governor_title = $governor ? get_post_meta($governor->ID, '_esp_governor_title', true) : '';
$governor_party = $governor ? get_post_meta($governor->ID, '_esp_governor_party', true) : '';
$governor_email = $governor ? get_post_meta($governor->ID, '_esp_governor_email', true) : '';
$governor_phone = $governor ? get_post_meta($governor->ID, '_esp_governor_phone', true) : '';
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Governor Profile Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage the Governor\'s profile information, photo, and official message', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <form method="post" action="">
        <?php wp_nonce_field('provincial_governor_nonce', 'provincial_nonce'); ?>
        
        <div class="esp-form-section">
            <h3><?php _e('Governor Information', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="governor_name"><?php _e('Full Name', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="governor_name" name="governor_name" 
                           value="<?php echo $governor ? esc_attr($governor->post_title) : ''; ?>" 
                           class="regular-text" required />
                    <div class="description"><?php _e('e.g., Hon. Allan Bird', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="governor_title"><?php _e('Official Title', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="governor_title" name="governor_title" 
                           value="<?php echo esc_attr($governor_title); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('e.g., Governor of the Province', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="governor_party"><?php _e('Political Party', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="governor_party" name="governor_party" 
                           value="<?php echo esc_attr($governor_party); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('e.g., Pangu Party', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="governor_email"><?php _e('Email Address', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="email" id="governor_email" name="governor_email" 
                           value="<?php echo esc_attr($governor_email); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Official email address for the Governor', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="governor_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="governor_phone" name="governor_phone" 
                           value="<?php echo esc_attr($governor_phone); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Official phone number for the Governor\'s office', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Governor\'s Message', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="governor_message"><?php _e('Official Message', 'esp-admin-manager'); ?></label>
                <div>
                    <?php
                    $content = $governor ? $governor->post_content : '';
                    wp_editor($content, 'governor_message', array(
                        'textarea_name' => 'governor_message',
                        'media_buttons' => false,
                        'textarea_rows' => 10,
                        'teeny' => true,
                        'quicktags' => false
                    ));
                    ?>
                    <div class="description"><?php _e('The Governor\'s official message to the people of the Province', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Governor\'s Photo', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label><?php _e('Current Photo', 'esp-admin-manager'); ?></label>
                <div>
                    <?php
                    $photo_id = $governor ? get_post_thumbnail_id($governor->ID) : 0;
                    $photo_url = $photo_id ? wp_get_attachment_image_url($photo_id, 'medium') : '';
                    ?>

                    <?php if ($photo_url): ?>
                        <div class="esp-media-preview">
                            <img src="<?php echo esc_url($photo_url); ?>" style="max-width: 300px; height: auto; border-radius: 8px; object-fit: cover;" alt="Governor Photo" />
                        </div>
                        <p>
                            <button type="button" class="button esp-upload-photo" data-target="governor-photo">
                                <?php _e('Change Photo', 'esp-admin-manager'); ?>
                            </button>
                            <input type="hidden" id="governor-photo" name="governor_photo" value="<?php echo esc_attr($photo_id); ?>" />
                            <div style="font-size: 11px; color: #666; margin-top: 5px;">
                                Photo ID: <?php echo $photo_id; ?>
                            </div>
                        </p>
                    <?php else: ?>
                        <div class="esp-media-upload">
                            <p><?php _e('No photo uploaded yet', 'esp-admin-manager'); ?></p>
                            <p>
                                <button type="button" class="button esp-upload-photo" data-target="governor-photo">
                                    <?php _e('Upload Photo', 'esp-admin-manager'); ?>
                                </button>
                                <input type="hidden" id="governor-photo" name="governor_photo" value="" />
                                <div style="font-size: 11px; color: #666; margin-top: 5px;">
                                    Photo ID: None
                                </div>
                            </p>
                        </div>
                    <?php endif; ?>
                    <div class="description">
                        <?php _e('Upload a professional photo of the Governor. Recommended size: 400x500 pixels.', 'esp-admin-manager'); ?>
                        <?php if ($governor): ?>
                            <br><?php _e('Click "Change Photo" or "Upload Photo" to manage the featured image in the post editor.', 'esp-admin-manager'); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <p class="submit">
            <input type="submit" name="submit" class="button-primary esp-button large" 
                   value="<?php _e('Save Governor Profile', 'esp-admin-manager'); ?>" />
        </p>
    </form>

    <!-- Preview Section -->
    <?php if ($governor): ?>
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the Governor profile will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px;">
            <style>
            .esp-preview .esp-governor-photo {
                max-width: 200px !important;
                max-height: 250px !important;
                margin: 0 auto 15px;
            }
            .esp-preview .esp-governor-photo img {
                max-width: 100% !important;
                max-height: 250px !important;
                width: auto !important;
                height: auto !important;
                object-fit: contain !important;
            }
            .esp-preview .esp-governor-grid {
                grid-template-columns: 200px 1fr !important;
                gap: 20px !important;
                align-items: start !important;
            }
            </style>
            <div class="esp-preview">
                <?php echo do_shortcode('[dakoii_governor]'); ?>
            </div>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong> 
            <code>[dakoii_governor]</code> <?php _e('(Recommended)', 'esp-admin-manager'); ?> 
            <small style="color: #666; margin-left: 10px;"><?php _e('or legacy', 'esp-admin-manager'); ?> <code>[esp_governor]</code></small>
        </p>
        <p>
            <?php _e('Copy and paste this shortcode into any page or post to display the Governor profile.', 'esp-admin-manager'); ?>
        </p>
    </div>
    <?php endif; ?>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Governor Profile Help', 'esp-admin-manager'); ?></h4>
        <p><?php _e('The Governor profile is a central feature of your provincial website. Here are some tips:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Keep the official message concise but meaningful (2-3 paragraphs recommended)', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use a high-quality, professional photo for the best presentation', 'esp-admin-manager'); ?></li>
            <li><?php _e('Update contact information regularly to ensure citizens can reach the Governor\'s office', 'esp-admin-manager'); ?></li>
            <li><?php _e('The Governor profile can be displayed using the [dakoii_governor] shortcode', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Auto-save functionality could be added here
    $('#governor_name, #governor_title, #governor_party').on('input', function() {
        // Add visual feedback for unsaved changes
        $('.submit .button-primary').css('background-color', '#d63638');
    });
    
    $('form').on('submit', function() {
        $('.submit .button-primary').css('background-color', '');
    });
});
</script>
